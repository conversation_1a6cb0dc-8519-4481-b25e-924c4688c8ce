# pyAHC项目HDF5集成方案

## 项目升级目标

将pyAHC项目改造升级为支持数据同化的水文模型框架，重点关注：

1. **数据管理架构升级** - 使用HDF5实现高效的状态变量存储和传递
2. **模型接口标准化** - 提供标准化的状态变量提取和设置接口
3. **工作流框架建设** - 支持逐日连续模拟和状态传递
4. **数据同化接口预留** - 为外部数据同化算法提供标准接口


## 1. 环境准备

### 1.1 确认依赖
检查 `pyproject.toml` 中已包含HDF5依赖：
```toml
h5py = "^3.11.0"
```

### 1.2 安装依赖
```bash
cd pyAHC
poetry install
# 或者
pip install h5py
```

## 2. HDF5数据组织结构设计

### 2.1 标准化数据组织结构

```
project.h5
├── corn_001-2013/                    # 项目组（玉米地块001-2013年）
│   ├── day_05-01/                    # 日期组（5月1日）
│   │   ├── input/                    # 模型输入(pickled Model对象)
│   │   ├── output/                   # 模型输出(pickled Result对象)
│   │   └── state-parameter_variables/ # 状态-参数变量
│   │       ├── soil_moisture         # 土壤含水量（状态变量）
│   │       ├── lai                   # 叶面积指数（状态变量）
│   │       ├── biomass               # 生物量（状态变量）
│   │       ├── root_depth            # 根深（状态变量）
│   │       ├── groundwater_level     # 地下水位（状态变量）
│   │       ├── param_hydraulic_conductivity # 土壤导水率（参数变量）
│   │       ├── param_crop_coefficient       # 作物系数（参数变量）
│   │       └── assimilated_*         # 数据同化后的变量
│   ├── day_05-02/                    # 5月2日
│   │   ├── input/                    # 包含前一天的状态-参数变量
│   │   ├── output/                   # 当天模拟结果
│   │   └── state-parameter_variables/ # 状态-参数变量
│   └── day_05-03/                    # 5月3日
│       └── ...                       # 类似结构
├── corn_002-2013/                    # 其他地块项目
│   └── ...
└── wheat_001-2014/                   # 其他作物项目
    └── ...
```

### 2.2 数据组织特点

1. **项目级别分组**: 按地块和年份组织（如 `corn_001-2013`）
2. **日期级别分组**: 按模拟日期组织（如 `day_05-01`）
3. **数据类型分组**:
   - `input/`: 模型输入对象
   - `output/`: 模型输出对象
   - `state-parameter_variables/`: 状态和参数变量
4. **变量类型区分**:
   - **状态变量**: 随时间变化的系统状态（土壤含水量、LAI等）
   - **参数变量**: 模型参数（土壤导水率、作物系数等）
5. **数据同化支持**: 预留 `assimilated_*` 前缀用于存储同化后的变量

## 3. HDF5状态管理器

### 3.1 扩展现有HDF5类

**重要：** 建议扩展现有的 `pyAHC/pyahc/db/hdf5.py` 文件，而不是创建新的状态管理器，以保持架构一致性。

修改现有的HDF5类，添加数据同化功能：

```python
import h5py
import numpy as np
import pandas as pd
from datetime import datetime, date
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import pickle
import logging

from pyahc.model.model import Model
from pyahc.model.result import Result

logger = logging.getLogger(__name__)


class StateVariableManager:
    """状态变量管理器 - 为数据同化提供标准化接口"""
    
    def __init__(self, db_path: str):
        """
        初始化状态变量管理器

        Args:
            db_path: HDF5数据库文件路径
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def save_simulation_state(self,
                            project_name: str,
                            simulation_date: date,
                            model: Model,
                            result: Result,
                            state_variables: Optional[Dict] = None):
        """
        保存模拟状态（为数据同化准备）

        Args:
            project_name: 项目名称（如 "corn_field_2013"）
            simulation_date: 模拟日期
            model: 模型对象
            result: 结果对象
            state_variables: 标准化的状态变量字典
        """
        date_str = simulation_date.strftime("%Y-%m-%d")
        
        with h5py.File(self.db_path, "a") as f:
            # 创建项目组
            project_group = f.require_group(project_name)
            
            # 创建日期组
            date_group = project_group.require_group(date_str)
            
            # 保存模型输入
            self._save_pickled_object(date_group, "model_input", model)
            
            # 保存模型输出
            self._save_pickled_object(date_group, "model_output", result)
            
            # 保存状态-参数变量
            if state_variables:
                self._save_state_parameter_variables(date_group, state_variables)

            # 保存元数据
            self._save_metadata(date_group, simulation_date, model)
            
        logger.info(f"已保存 {project_name} 项目 {date_str} 的模拟状态")

    def load_simulation_state(self,
                            project_name: str,
                            simulation_date: date) -> Tuple[Model, Result, Dict]:
        """
        加载模拟状态（为数据同化准备）

        Args:
            project_name: 项目名称
            simulation_date: 模拟日期

        Returns:
            (模型对象, 结果对象, 标准化状态变量字典)
        """
        date_str = simulation_date.strftime("%Y-%m-%d")
        
        with h5py.File(self.db_path, "r") as f:
            try:
                date_group = f[project_name][date_str]
                
                # 加载模型和结果
                model = self._load_pickled_object(date_group, "model_input")
                result = self._load_pickled_object(date_group, "model_output")
                
                # 加载状态-参数变量
                state_variables = self._load_state_parameter_variables(date_group)
                
                return model, result, state_variables

            except KeyError:
                raise KeyError(f"未找到项目 {project_name} 日期 {date_str} 的数据")

    def get_state_for_assimilation(self,
                                 project_name: str,
                                 current_date: date) -> Optional[Dict]:
        """
        获取用于数据同化的状态变量（通常是前一天的状态）

        Args:
            project_name: 项目名称
            current_date: 当前日期

        Returns:
            标准化的状态-参数变量字典，供外部数据同化算法使用
        """
        from datetime import timedelta
        previous_date = current_date - timedelta(days=1)
        
        try:
            _, _, state_variables = self.load_simulation_state(project_name, previous_date)
            return state_variables
        except KeyError:
            logger.warning(f"未找到 {previous_date} 的状态-参数变量")
            return None

    def apply_assimilated_state(self,
                              project_name: str,
                              simulation_date: date,
                              assimilated_state: Dict):
        """
        应用数据同化后的状态-参数变量（接口预留）

        Args:
            project_name: 项目名称
            simulation_date: 模拟日期
            assimilated_state: 经过数据同化处理的状态-参数变量
        """
        # 这里预留接口，供外部数据同化算法调用
        # 可以保存同化后的状态，或者直接传递给下一次模拟
        date_str = simulation_date.strftime("%Y-%m-%d")

        with h5py.File(self.db_path, "a") as f:
            project_group = f.require_group(project_name)
            date_group = project_group.require_group(date_str)

            # 保存同化后的状态-参数变量
            self._save_state_parameter_variables(date_group, assimilated_state, prefix="assimilated_")

        logger.info(f"已应用数据同化状态: {project_name} {date_str}")
    
    def list_simulation_dates(self, project_name: str) -> List[date]:
        """
        列出项目中所有的模拟日期
        
        Args:
            project_name: 项目名称
            
        Returns:
            日期列表
        """
        dates = []
        
        with h5py.File(self.db_path, "r") as f:
            if project_name in f:
                for date_str in f[project_name].keys():
                    try:
                        dates.append(datetime.strptime(date_str, "%Y-%m-%d").date())
                    except ValueError:
                        continue
        
        return sorted(dates)
    
    def _save_pickled_object(self, group: h5py.Group, name: str, obj: Any):
        """保存Python对象为pickle格式"""
        try:
            if name in group:
                del group[name]
            
            pickled_data = pickle.dumps(obj)
            group.create_dataset(name, data=np.void(pickled_data))
            
        except Exception as e:
            logger.error(f"保存对象 {name} 失败: {e}")
    
    def _load_pickled_object(self, group: h5py.Group, name: str) -> Any:
        """加载pickle格式的Python对象"""
        try:
            pickled_data = group[name][()].tobytes()
            return pickle.loads(pickled_data)
        except Exception as e:
            logger.error(f"加载对象 {name} 失败: {e}")
            return None
    
    def _save_state_parameter_variables(self, group: h5py.Group, state_vars: Dict, prefix: str = ""):
        """保存状态-参数变量"""
        state_group = group.require_group("state-parameter_variables")

        for var_name, var_value in state_vars.items():
            try:
                full_name = f"{prefix}{var_name}"
                if full_name in state_group:
                    del state_group[full_name]

                if isinstance(var_value, (list, np.ndarray)):
                    state_group.create_dataset(full_name, data=np.array(var_value))
                else:
                    state_group.create_dataset(full_name, data=var_value)

            except Exception as e:
                logger.error(f"保存状态-参数变量 {var_name} 失败: {e}")

    def _load_state_parameter_variables(self, group: h5py.Group) -> Dict:
        """加载状态-参数变量"""
        state_vars = {}

        if "state-parameter_variables" in group:
            state_group = group["state-parameter_variables"]
            for var_name in state_group.keys():
                try:
                    data = state_group[var_name][()]
                    if isinstance(data, np.ndarray) and data.ndim == 0:
                        state_vars[var_name] = data.item()
                    else:
                        state_vars[var_name] = data
                except Exception as e:
                    logger.error(f"加载状态-参数变量 {var_name} 失败: {e}")
        
        return state_vars
    
    def _save_metadata(self, group: h5py.Group, simulation_date: date, model: Model):
        """保存元数据"""
        metadata = {
            "simulation_date": simulation_date.isoformat(),
            "created_at": datetime.now().isoformat(),
            "model_version": getattr(model, 'version', 'unknown'),
            "project_name": getattr(model.metadata, 'project_name', 'unknown') if hasattr(model, 'metadata') else 'unknown'
        }
        
        for key, value in metadata.items():
            if isinstance(value, str):
                group.attrs[key] = value
```

## 4. 标准化状态-参数变量接口

### 4.1 创建配置驱动的状态变量提取器

**改进建议：** 使用配置文件驱动的变量映射，提高灵活性和可维护性。

创建文件 `pyAHC/pyahc/utils/state_extractor.py`：

```python
import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import logging

from pyahc.model.result import Result

logger = logging.getLogger(__name__)


class StateVariableExtractor:
    """标准化状态-参数变量提取器 - 为数据同化提供统一接口"""
    
    @staticmethod
    def extract_all_state_variables(result: Result) -> Dict:
        """
        提取所有关键状态-参数变量（标准化格式）

        Args:
            result: 模型结果对象

        Returns:
            标准化的状态-参数变量字典，键名统一，便于数据同化算法使用
        """
        state_vars = {}
        
        try:
            # 提取土壤含水量（状态变量）
            soil_moisture = StateVariableExtractor.extract_soil_moisture(result)
            if soil_moisture is not None:
                state_vars['soil_moisture'] = soil_moisture

            # 提取作物LAI（状态变量）
            lai = StateVariableExtractor.extract_lai(result)
            if lai is not None:
                state_vars['lai'] = lai

            # 提取生物量（状态变量）
            biomass = StateVariableExtractor.extract_biomass(result)
            if biomass is not None:
                state_vars['biomass'] = biomass

            # 提取根深（状态变量）
            root_depth = StateVariableExtractor.extract_root_depth(result)
            if root_depth is not None:
                state_vars['root_depth'] = root_depth

            # 提取地下水位（状态变量）
            groundwater_level = StateVariableExtractor.extract_groundwater_level(result)
            if groundwater_level is not None:
                state_vars['groundwater_level'] = groundwater_level

            # 提取模型参数（参数变量）
            model_parameters = StateVariableExtractor.extract_model_parameters(result)
            if model_parameters is not None:
                state_vars.update(model_parameters)

        except Exception as e:
            logger.error(f"提取状态-参数变量时出错: {e}")
        
        return state_vars
    
    @staticmethod
    def extract_soil_moisture(result: Result) -> Optional[List[float]]:
        """提取土壤含水量"""
        try:
            if result.csv is not None:
                # 从CSV输出中提取最后一天的土壤含水量
                df = result.csv
                if not df.empty:
                    # 查找土壤含水量相关列
                    moisture_cols = [col for col in df.columns if 'theta' in col.lower() or 'moisture' in col.lower()]
                    if moisture_cols:
                        last_row = df.iloc[-1]
                        return [last_row[col] for col in moisture_cols]
            
            # 如果CSV中没有，尝试从其他输出格式中提取
            if result.output:
                for key, data in result.output.items():
                    if 'moisture' in key.lower() or 'theta' in key.lower():
                        # 处理不同格式的数据
                        if isinstance(data, pd.DataFrame):
                            return data.iloc[-1].tolist()
                        elif isinstance(data, (list, np.ndarray)):
                            return list(data)
            
        except Exception as e:
            logger.error(f"提取土壤含水量失败: {e}")
        
        return None
    
    @staticmethod
    def extract_lai(result: Result) -> Optional[float]:
        """提取叶面积指数"""
        try:
            if result.csv is not None:
                df = result.csv
                if not df.empty:
                    lai_cols = [col for col in df.columns if 'lai' in col.lower()]
                    if lai_cols:
                        return float(df.iloc[-1][lai_cols[0]])
            
            # 从其他输出格式中查找
            if result.output:
                for key, data in result.output.items():
                    if 'lai' in key.lower():
                        if isinstance(data, pd.DataFrame):
                            return float(data.iloc[-1].iloc[0])
                        elif isinstance(data, (int, float)):
                            return float(data)
                        elif isinstance(data, (list, np.ndarray)) and len(data) > 0:
                            return float(data[-1])
            
        except Exception as e:
            logger.error(f"提取LAI失败: {e}")
        
        return None
    
    @staticmethod
    def extract_biomass(result: Result) -> Optional[float]:
        """提取生物量"""
        try:
            if result.csv is not None:
                df = result.csv
                if not df.empty:
                    biomass_cols = [col for col in df.columns if 'biomass' in col.lower() or 'bio' in col.lower()]
                    if biomass_cols:
                        return float(df.iloc[-1][biomass_cols[0]])
            
        except Exception as e:
            logger.error(f"提取生物量失败: {e}")
        
        return None
    
    @staticmethod
    def extract_root_depth(result: Result) -> Optional[float]:
        """提取根深"""
        try:
            if result.csv is not None:
                df = result.csv
                if not df.empty:
                    root_cols = [col for col in df.columns if 'root' in col.lower() and 'depth' in col.lower()]
                    if root_cols:
                        return float(df.iloc[-1][root_cols[0]])
            
        except Exception as e:
            logger.error(f"提取根深失败: {e}")
        
        return None
    
    @staticmethod
    def extract_groundwater_level(result: Result) -> Optional[float]:
        """提取地下水位"""
        try:
            if result.csv is not None:
                df = result.csv
                if not df.empty:
                    gw_cols = [col for col in df.columns if 'gwl' in col.lower() or 'groundwater' in col.lower()]
                    if gw_cols:
                        return float(df.iloc[-1][gw_cols[0]])
            
        except Exception as e:
            logger.error(f"提取地下水位失败: {e}")

        return None

    @staticmethod
    def extract_model_parameters(result: Result) -> Optional[Dict]:
        """提取模型参数（参数变量）"""
        try:
            parameters = {}

            # 提取土壤水力参数
            if result.output:
                for key, data in result.output.items():
                    if 'param' in key.lower() or 'parameter' in key.lower():
                        if isinstance(data, pd.DataFrame):
                            # 从DataFrame中提取参数
                            param_cols = [col for col in data.columns if 'param' in col.lower()]
                            for col in param_cols:
                                parameters[f"param_{col}"] = float(data.iloc[-1][col])
                        elif isinstance(data, (int, float)):
                            parameters[key] = float(data)

            # 可以添加更多参数提取逻辑
            # 例如：土壤导水率、作物参数等

            return parameters if parameters else None

        except Exception as e:
            logger.error(f"提取模型参数失败: {e}")

        return None
```

### 4.2 创建状态-参数变量设置器

创建文件 `pyAHC/pyahc/utils/state_setter.py`：

```python
from typing import Dict, Optional
import logging

from pyahc.model.model import Model

logger = logging.getLogger(__name__)


class StateVariableSetter:
    """状态-参数变量设置器 - 将标准化状态-参数变量应用到模型"""

    @staticmethod
    def apply_state_to_model(model: Model, state_variables: Dict) -> Model:
        """
        将状态-参数变量应用到模型（为下一次模拟准备）

        Args:
            model: 模型对象
            state_variables: 标准化的状态-参数变量字典

        Returns:
            更新后的模型对象
        """
        try:
            # 更新土壤含水量
            if 'soil_moisture' in state_variables and model.soilmoisture:
                model.soilmoisture.thetai = state_variables['soil_moisture']
                logger.info(f"✓ 更新土壤含水量: {len(state_variables['soil_moisture'])} 层")

            # 更新作物LAI（根据模型结构调整）
            if 'lai' in state_variables:
                # 这里需要根据具体的模型结构来设置LAI
                # 可能需要设置到作物组件的初始LAI参数
                logger.info(f"✓ 准备更新LAI: {state_variables['lai']}")

            # 更新生物量
            if 'biomass' in state_variables:
                # 根据模型结构设置初始生物量
                logger.info(f"✓ 准备更新生物量: {state_variables['biomass']}")

            # 更新根深
            if 'root_depth' in state_variables:
                logger.info(f"✓ 准备更新根深: {state_variables['root_depth']}")

            # 更新地下水位
            if 'groundwater_level' in state_variables:
                # 可能需要更新到边界条件组件
                logger.info(f"✓ 准备更新地下水位: {state_variables['groundwater_level']}")

        except Exception as e:
            logger.error(f"应用状态变量时出错: {e}")

        return model

    @staticmethod
    def create_assimilation_interface(state_variables: Dict) -> Dict:
        """
        创建数据同化接口格式

        Args:
            state_variables: 模型状态变量

        Returns:
            适合外部数据同化算法的标准格式
        """
        # 转换为数据同化算法期望的格式
        assimilation_format = {
            'state_vector': [],
            'variable_names': [],
            'variable_types': [],
            'dimensions': []
        }

        for var_name, var_value in state_variables.items():
            assimilation_format['variable_names'].append(var_name)

            if isinstance(var_value, list):
                assimilation_format['state_vector'].extend(var_value)
                assimilation_format['variable_types'].append('array')
                assimilation_format['dimensions'].append(len(var_value))
            else:
                assimilation_format['state_vector'].append(var_value)
                assimilation_format['variable_types'].append('scalar')
                assimilation_format['dimensions'].append(1)

        return assimilation_format

    @staticmethod
    def parse_assimilated_result(assimilated_data: Dict,
                               original_format: Dict) -> Dict:
        """
        解析数据同化结果，转换回模型格式

        Args:
            assimilated_data: 数据同化算法的输出
            original_format: 原始状态变量格式

        Returns:
            标准化的状态变量字典
        """
        # 这里提供接口，将外部数据同化算法的结果转换回模型可用的格式
        # 具体实现取决于使用的数据同化算法

        result_state = {}

        # 示例：简单的格式转换
        if 'state_vector' in assimilated_data:
            state_vector = assimilated_data['state_vector']
            var_names = original_format.get('variable_names', [])
            dimensions = original_format.get('dimensions', [])

            idx = 0
            for i, var_name in enumerate(var_names):
                dim = dimensions[i]
                if dim == 1:
                    result_state[var_name] = state_vector[idx]
                    idx += 1
                else:
                    result_state[var_name] = state_vector[idx:idx+dim]
                    idx += dim

        return result_state
```

## 5. 模拟工作流管理器

创建文件 `pyAHC/pyahc/utils/simulation_workflow.py`：

```python
from datetime import date, timedelta
from typing import Dict, Optional, Callable, List
import logging

from pyahc.model.model import Model
from pyahc.db.state_manager import StateVariableManager
from pyahc.utils.state_extractor import StateVariableExtractor
from pyahc.utils.state_setter import StateVariableSetter

logger = logging.getLogger(__name__)


class SimulationWorkflow:
    """模拟工作流管理器 - 支持数据同化的连续模拟"""
    
    def __init__(self,
                 db_path: str,
                 project_name: str,
                 model_creator: Callable[[date, date, Optional[Dict]], Model]):
        """
        初始化模拟工作流

        Args:
            db_path: HDF5数据库路径
            project_name: 项目名称
            model_creator: 模型创建函数，接受(start_date, end_date, initial_state)参数
        """
        self.state_manager = StateVariableManager(db_path)
        self.project_name = project_name
        self.model_creator = model_creator
        self.extractor = StateVariableExtractor()
        self.setter = StateVariableSetter()
    
    def run_daily_simulation(self,
                           simulation_date: date,
                           external_assimilation_func: Optional[Callable] = None) -> Dict:
        """
        运行单日模拟（支持外部数据同化）

        Args:
            simulation_date: 模拟日期
            external_assimilation_func: 外部数据同化函数（可选）
                                      函数签名: func(state_dict) -> assimilated_state_dict

        Returns:
            标准化状态变量字典
        """
        # 获取前一天的状态变量
        previous_state = self.state_manager.get_state_for_assimilation(
            self.project_name, simulation_date
        )

        # 如果有外部数据同化函数，应用数据同化
        if previous_state and external_assimilation_func:
            try:
                # 转换为数据同化接口格式
                assimilation_format = self.setter.create_assimilation_interface(previous_state)

                # 调用外部数据同化算法
                assimilated_format = external_assimilation_func(assimilation_format)

                # 转换回模型格式
                previous_state = self.setter.parse_assimilated_result(
                    assimilated_format, assimilation_format
                )

                # 保存同化后的状态
                self.state_manager.apply_assimilated_state(
                    self.project_name, simulation_date, previous_state
                )

                logger.info(f"对 {simulation_date} 应用了外部数据同化")
            except Exception as e:
                logger.error(f"数据同化失败: {e}")

        # 创建模型
        end_date = simulation_date + timedelta(days=1)
        model = self.model_creator(simulation_date, end_date, previous_state)

        # 应用状态变量到模型
        if previous_state:
            model = self.setter.apply_state_to_model(model, previous_state)

        # 运行模型
        result = model.run()

        # 提取状态变量
        state_variables = self.extractor.extract_all_state_variables(result)

        # 保存结果
        self.state_manager.save_simulation_state(
            self.project_name, simulation_date, model, result, state_variables
        )

        logger.info(f"完成 {simulation_date} 的模拟")
        return state_variables
    
    def run_period_simulation(self,
                            start_date: date,
                            end_date: date,
                            external_assimilation_func: Optional[Callable] = None):
        """
        运行时间段连续模拟

        Args:
            start_date: 开始日期
            end_date: 结束日期
            external_assimilation_func: 外部数据同化函数（可选）
        """
        current_date = start_date

        while current_date <= end_date:
            try:
                self.run_daily_simulation(
                    current_date, external_assimilation_func
                )
            except Exception as e:
                logger.error(f"模拟 {current_date} 时出错: {e}")

            current_date += timedelta(days=1)
    
    def get_simulation_summary(self) -> Dict:
        """获取模拟摘要"""
        dates = self.state_manager.list_simulation_dates(self.project_name)
        
        if not dates:
            return {"message": "没有找到模拟数据"}
        
        return {
            "project_name": self.project_name,
            "simulation_dates": dates,
            "start_date": min(dates),
            "end_date": max(dates),
            "total_days": len(dates)
        }

    def get_assimilation_interface(self, simulation_date: date) -> Optional[Dict]:
        """
        获取数据同化接口（供外部算法调用）

        Args:
            simulation_date: 模拟日期

        Returns:
            标准化的数据同化接口格式
        """
        state_vars = self.state_manager.get_state_for_assimilation(
            self.project_name, simulation_date
        )

        if state_vars:
            return self.setter.create_assimilation_interface(state_vars)
        return None
```

## 6. 项目升级使用示例

### 6.1 基础状态-参数管理示例

创建文件 `pyAHC/examples/state_management_example.py`：

```python
#!/usr/bin/env python3
"""HDF5数据同化使用示例"""

import sys
from pathlib import Path
from datetime import date, datetime, timedelta
import numpy as np

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from pyahc.db.state_manager import StateVariableManager
from pyahc.utils.state_extractor import StateVariableExtractor
from pyahc.utils.state_setter import StateVariableSetter
from pyahc.utils.simulation_workflow import SimulationWorkflow
from pyahc.model.model import Model, ModelBuilder
from hetao_corn_2013 import create_model_components


def create_model_with_initial_state(start_date: date,
                                   end_date: date,
                                   initial_state: dict = None) -> Model:
    """
    创建带有初始状态的模型

    Args:
        start_date: 开始日期
        end_date: 结束日期
        initial_state: 初始状态变量字典

    Returns:
        配置好的模型对象
    """
    # 使用现有的模型创建函数
    model, _, _, _, _ = create_model_components()

    # 更新模拟时间
    model.generalsettings.tstart = start_date
    model.generalsettings.tend = end_date

    # 如果有初始状态，更新模型参数
    if initial_state:
        update_model_initial_conditions(model, initial_state)

    return model


def external_assimilation_example(assimilation_interface: Dict) -> Dict:
    """
    外部数据同化算法示例接口

    Args:
        assimilation_interface: 标准化的数据同化接口

    Returns:
        同化后的数据（相同格式）
    """
    # 这里是外部数据同化算法的接口示例
    # 实际使用时，这里会调用具体的数据同化算法（如EnKF、3DVar等）

    print("🔄 调用外部数据同化算法...")
    print(f"  输入变量: {assimilation_interface['variable_names']}")
    print(f"  状态向量长度: {len(assimilation_interface['state_vector'])}")

    # 模拟数据同化过程（实际中会被真实的算法替换）
    assimilated_interface = assimilation_interface.copy()

    # 示例：对状态向量进行简单的调整
    state_vector = assimilated_interface['state_vector'].copy()
    for i in range(len(state_vector)):
        # 添加小的随机扰动模拟同化效果
        state_vector[i] = state_vector[i] * (1 + np.random.normal(0, 0.01))

    assimilated_interface['state_vector'] = state_vector

    print("✓ 数据同化完成")
    return assimilated_interface


def example_1_basic_usage():
    """示例1: 基础HDF5使用"""
    print("=== 示例1: 基础HDF5使用 ===")

    # 初始化HDF5状态管理器
    state_manager = StateVariableManager("data/corn_simulation.h5")

    # 创建模型并运行
    start_date = date(2013, 5, 1)
    end_date = date(2013, 5, 2)

    model = create_model_with_initial_state(start_date, end_date)

    # 运行模型（这里使用模拟的结果）
    print("运行模型...")
    # result = model.run()  # 实际运行

    # 模拟结果对象（用于演示）
    from pyahc.model.result import Result
    import pandas as pd

    # 创建模拟的CSV数据
    mock_csv = pd.DataFrame({
        'DATETIME': [datetime(2013, 5, 2)],
        'theta_1': [0.25],
        'theta_2': [0.30],
        'theta_3': [0.35],
        'lai': [2.5],
        'biomass': [1500.0],
        'gwl': [-150.0]
    })
    mock_csv.set_index('DATETIME', inplace=True)

    result = Result(
        log="模拟运行日志",
        output={'csv': mock_csv},
        warning=[]
    )

    # 提取状态变量
    extractor = StateVariableExtractor()
    state_vars = extractor.extract_all_state_variables(result)
    print(f"提取的状态变量: {list(state_vars.keys())}")

    # 保存到HDF5
    state_manager.save_simulation_state(
        project_name="corn_001-2013",
        simulation_date=start_date,
        model=model,
        result=result,
        state_variables=state_vars
    )

    print("✓ 数据已保存到HDF5")

    # 加载数据
    loaded_model, loaded_result, loaded_state = state_manager.load_simulation_state(
        "corn_001-2013", start_date
    )

    print(f"✓ 数据已从HDF5加载")
    print(f"  加载的状态变量: {list(loaded_state.keys())}")


def example_2_workflow_usage():
    """示例2: 数据同化工作流使用"""
    print("\n=== 示例2: 数据同化工作流使用 ===")

    # 初始化工作流
    workflow = DataAssimilationWorkflow(
        db_path="data/corn_workflow.h5",
        project_name="corn_field_2013",
        model_creator=create_model_with_initial_state
    )

    # 准备观测数据
    observations_data = {
        date(2013, 5, 2): {
            'soil_moisture_obs': [0.26, 0.31, 0.36],
            'lai_obs': 2.3
        },
        date(2013, 5, 4): {
            'soil_moisture_obs': [0.24, 0.29, 0.34],
            'lai_obs': 2.8
        }
    }

    # 运行时间段模拟
    start_date = date(2013, 5, 1)
    end_date = date(2013, 5, 5)

    print(f"运行 {start_date} 到 {end_date} 的模拟...")

    workflow.run_period_simulation(
        start_date=start_date,
        end_date=end_date,
        observations_dict=observations_data,
        assimilation_func=simple_data_assimilation
    )

    # 获取模拟摘要
    summary = workflow.get_simulation_summary()
    print(f"✓ 模拟完成")
    print(f"  项目: {summary['project_name']}")
    print(f"  模拟天数: {summary['total_days']}")
    print(f"  日期范围: {summary['start_date']} 到 {summary['end_date']}")


def example_3_manual_daily_simulation():
    """示例3: 手动逐日模拟"""
    print("\n=== 示例3: 手动逐日模拟 ===")

    db = DataAssimilationHDF5("data/manual_simulation.h5")
    project_name = "manual_corn_2013"

    # 第一天模拟（无前置状态）
    day1 = date(2013, 5, 1)
    print(f"模拟第1天: {day1}")

    model1 = create_model_with_initial_state(day1, day1 + timedelta(days=1))
    # result1 = model1.run()  # 实际运行

    # 模拟结果
    mock_result1 = create_mock_result(day1, lai=2.0, biomass=1200)
    state_vars1 = StateVariableExtractor.extract_all_state_variables(mock_result1)

    db.save_daily_simulation(project_name, day1, model1, mock_result1, state_vars1)

    # 第二天模拟（使用前一天的状态）
    day2 = date(2013, 5, 2)
    print(f"模拟第2天: {day2}")

    # 获取前一天的状态
    previous_state = db.get_previous_state_variables(project_name, day2)
    print(f"  前一天状态变量: {list(previous_state.keys()) if previous_state else 'None'}")

    # 应用观测数据同化
    if previous_state:
        observations = {'lai_obs': 2.2, 'soil_moisture_obs': [0.25, 0.30, 0.35]}
        assimilated_state = simple_data_assimilation(previous_state, observations)
        print(f"  应用数据同化")
    else:
        assimilated_state = None

    model2 = create_model_with_initial_state(day2, day2 + timedelta(days=1), assimilated_state)
    mock_result2 = create_mock_result(day2, lai=2.4, biomass=1400)
    state_vars2 = StateVariableExtractor.extract_all_state_variables(mock_result2)

    db.save_daily_simulation(project_name, day2, model2, mock_result2, state_vars2)

    # 查看所有模拟日期
    all_dates = db.list_simulation_dates(project_name)
    print(f"✓ 完成模拟，共 {len(all_dates)} 天: {all_dates}")


def create_mock_result(simulation_date: date, lai: float, biomass: float) -> 'Result':
    """创建模拟的结果对象用于演示"""
    from pyahc.model.result import Result
    import pandas as pd

    mock_csv = pd.DataFrame({
        'DATETIME': [datetime.combine(simulation_date, datetime.min.time())],
        'theta_1': [0.25 + np.random.normal(0, 0.02)],
        'theta_2': [0.30 + np.random.normal(0, 0.02)],
        'theta_3': [0.35 + np.random.normal(0, 0.02)],
        'lai': [lai],
        'biomass': [biomass],
        'gwl': [-150.0 + np.random.normal(0, 5)]
    })
    mock_csv.set_index('DATETIME', inplace=True)

    return Result(
        log=f"模拟日期: {simulation_date}",
        output={'csv': mock_csv},
        warning=[]
    )


if __name__ == "__main__":
    # 创建数据目录
    Path("data").mkdir(exist_ok=True)

    # 运行示例
    try:
        example_1_basic_usage()
        example_2_workflow_usage()
        example_3_manual_daily_simulation()

        print("\n🎉 所有示例运行完成！")
        print("HDF5文件已保存在 data/ 目录中")

    except Exception as e:
        print(f"❌ 运行示例时出错: {e}")
        import traceback
        traceback.print_exc()
```

### 6.2 与现有hetao_corn_2013.py集成

修改现有的 `hetao_corn_2013.py` 文件，添加HDF5支持：

```python
# 在文件开头添加导入
from pyahc.db.data_assimilation import DataAssimilationHDF5
from pyahc.utils.state_extractor import StateVariableExtractor
from pyahc.utils.data_assimilation_workflow import DataAssimilationWorkflow

def run_ahc_simulation_with_hdf5(output_dir=None,
                                use_validation=True,
                                enable_hdf5=True,
                                hdf5_path="simulation_results.h5"):
    """
    运行带HDF5支持的AHC模拟流程

    Args:
        output_dir: 输出目录
        use_validation: 是否使用验证框架
        enable_hdf5: 是否启用HDF5存储
        hdf5_path: HDF5文件路径
    """
    print(f"=== 开始 AHC-V201.exe 执行案例（HDF5支持：{enable_hdf5}）===")

    # 初始化HDF5数据库（如果启用）
    hdf5_db = None
    if enable_hdf5:
        hdf5_db = DataAssimilationHDF5(hdf5_path)
        print(f"✓ HDF5数据库初始化: {hdf5_path}")

    # 创建输出目录
    if output_dir is None:
        project_root = Path(__file__).parent
        temp_dir_path = project_root / "temp_hetao_corn_2013"
        temp_dir_path.mkdir(exist_ok=True)
        output_dir = str(temp_dir_path)

    try:
        # 1. 创建模型组件
        print("创建模型组件...")
        model, _, epic_crop, obs_points, meteo_manager = create_model_components()

        # 2. 生成输入文件
        generate_input_files(model, epic_crop, obs_points, output_dir)

        # 3. 运行 AHC 可执行文件
        success = run_ahc_executable(output_dir)

        # 4. 检查输出结果
        check_output_files(output_dir)

        if success and enable_hdf5:
            # 5. 处理HDF5存储
            print("\n处理HDF5存储...")

            # 读取模型结果
            result = read_model_results(output_dir)

            # 提取状态变量
            extractor = StateVariableExtractor()
            state_vars = extractor.extract_all_state_variables(result)

            # 保存到HDF5
            simulation_date = model.generalsettings.tstart
            hdf5_db.save_daily_simulation(
                project_name="hetao_corn_2013",
                simulation_date=simulation_date,
                model=model,
                result=result,
                state_variables=state_vars
            )

            print(f"✓ 结果已保存到HDF5: {hdf5_path}")
            print(f"  状态变量: {list(state_vars.keys())}")

        if success:
            print("\n✓ AHC 模拟执行成功！")
        else:
            print("\n✗ AHC 模拟执行失败")

        return output_dir, hdf5_db

    except Exception as e:
        print(f"\n✗ 执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return output_dir, hdf5_db


def read_model_results(output_dir: str) -> 'Result':
    """从输出目录读取模型结果"""
    from pyahc.model.result import Result
    import pandas as pd

    output_path = Path(output_dir)

    # 读取CSV输出文件
    csv_files = list(output_path.glob("rs0*output.csv"))
    csv_data = None

    if csv_files:
        try:
            csv_data = pd.read_csv(csv_files[0], comment="*", index_col=0)
            csv_data.index = pd.to_datetime(csv_data.index)
        except Exception as e:
            print(f"读取CSV文件失败: {e}")

    # 读取日志文件
    log_content = ""
    log_file = output_path / "AHC.LOG"
    if log_file.exists():
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                log_content = f.read()
        except Exception as e:
            print(f"读取日志文件失败: {e}")

    # 创建Result对象
    output_dict = {}
    if csv_data is not None:
        output_dict['csv'] = csv_data

    return Result(
        log=log_content,
        output=output_dict,
        warning=[]
    )


# 修改main函数
def main():
    """主函数 - 展示HDF5集成的使用"""
    print("=== pyAHC HDF5集成演示 ===")

    try:
        # 运行带HDF5支持的模拟
        output_directory, hdf5_db = run_ahc_simulation_with_hdf5(
            use_validation=True,
            enable_hdf5=True,
            hdf5_path="hetao_corn_simulation.h5"
        )

        if hdf5_db:
            # 展示HDF5功能
            dates = hdf5_db.list_simulation_dates("hetao_corn_2013")
            print(f"\nHDF5中的模拟日期: {dates}")

            if dates:
                # 加载第一个日期的数据
                model, result, state_vars = hdf5_db.load_daily_simulation(
                    "hetao_corn_2013", dates[0]
                )
                print(f"加载的状态变量: {list(state_vars.keys())}")

        print(f"\n案例执行完成，结果保存在: {output_directory}")
        return 0

    except Exception as e:
        print(f"✗ 错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
```

## 7. 实际部署步骤

### 7.1 创建必要的目录结构
```bash
cd pyAHC
mkdir -p pyahc/db
mkdir -p pyahc/utils
mkdir -p examples
mkdir -p data
```

### 7.2 安装和测试
```bash
# 安装依赖
poetry install

# 运行示例
python examples/hdf5_data_assimilation_example.py

# 运行集成的hetao_corn_2013.py
python hetao_corn_2013.py
```

### 7.3 验证HDF5文件
```python
import h5py

# 查看HDF5文件结构
with h5py.File("data/corn_simulation.h5", "r") as f:
    def print_structure(name, obj):
        print(name)
    f.visititems(print_structure)
```

## 8. 注意事项

1. **数据备份**: HDF5文件包含重要的模拟数据，建议定期备份
2. **文件大小**: 长期模拟会产生大文件，考虑定期归档
3. **版本兼容**: 确保HDF5库版本兼容性
4. **错误处理**: 实现完善的错误处理和日志记录
5. **性能优化**: 对于大量数据，考虑使用HDF5的压缩和分块功能

这套方案提供了完整的HDF5集成解决方案，支持数据同化工作流的所有需求。

